import cv2
import numpy as np
import pyrealsense2 as rs
import rospy
from std_msgs.msg import Float64MultiArray
from utils.yolov5 import YoloV5
from utils.function import filter_wrong_class,find_red_pixel_center,find_blue_pixel_center, pixel2real
from std_msgs.msg import Float64MultiArray

CLASSES = ['hxzq','lltd','ysxtl', 'dyxa', 'akhm', 'box', 'medicine_back']
VISUALIZE= False  # 设置为False以避免显示问题
COLOR_THRESHOLD = {"box":30, "lltd":30, "hxzq":30, "ysxtl":30, "dyxa":30, "akhm":30, "medicine_back":30} #颜色过滤阈值

d = np.array([[0 , 0 ,1],
              [-1,0,0],
              [0,-1,0]]) #相机坐标方向变化矩阵
r = np.array([[1, 0 , 0],
              [0, 0.4473, 0.8944],
              [0, -0.8944, 0.4473]]) # 相机坐标旋转变换矩阵
t = np.array([0.1067, 0.02674, -0.02499]) # 相机坐标平移向量
KV = np.eye(4) # 相机外参矩阵
KV[:3, :3] = np.dot(d, r) # 计算相机相对原点的旋转矩阵
KV[:3, 3] = t            
K = np.array([[606.7, 0, 314.6],
              [0, 606.7, 252.5],
              [0, 0, 1]])

PALETTE = [(255, 0, 0),      # 红色
            (0, 255, 0),     # 绿色
            (0, 0, 255),     # 蓝色
            (255, 255, 0),   # 黄色
            (0, 255, 255),   # 青色
            (255, 0, 255),   # 品红色
            (128, 0, 0),     # 深红色
            (0, 128, 0),     # 深绿色
            (0, 0, 128),     # 深蓝色
            (128, 128, 0),   # 黄绿色
            (0, 128, 128),]  # 青色系
            

def publish_detect(graph_point_pose=None):
    # 创建图片cv_bridge实例及消息
    # img_msg = CvBridge().cv2_to_imgmsg(img, "bgr8")
    # global flag
    # 发布图片到'image_topic'，发布字符串到'string_topic'
    # rospy.Publisher("gripper_det_img", Image, queue_size=10).publish(img_msg)
    rospy.Publisher('gripper_det_box', Float64MultiArray, queue_size=10).publish(graph_point_pose)


def visualize_yolo(image, results, centers=None):
    """
    基于检测框、位置点、检测置信度、类别可视化图像
    """
    for result in results:
        box = result[0:4].astype(np.int16)
        cls = result[5].astype(np.int8)
        threshold = result[4].astype(np.float16)

        # 检查类别索引是否在有效范围内
        if cls < 0 or cls >= len(CLASSES) or cls >= len(PALETTE):
            print(f"可视化时检测到无效的类别索引: {cls}, 跳过该检测结果")
            continue

        cv2.rectangle(image, (box[0], box[1]), (box[2], box[3]), color=PALETTE[cls],thickness=3)
        cv2.putText(image, '{0} {1:.2f}'.format(CLASSES[cls], threshold),
                    (box[0], box[1]),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.6, PALETTE[cls], 2)
    if centers:
        for i, center in enumerate(centers):
            # 使用安全的颜色索引
            color_idx = min(i % len(PALETTE), len(PALETTE) - 1)
            cv2.circle(image, center, radius=3, color=PALETTE[color_idx], thickness=-1)
    return image


if __name__ == "__main__":
    # 定义ros节点
    rospy.init_node('Detect_Result', anonymous=True)

    # 定义相机拉流相关配置
    pipeline = rs.pipeline()
    # 定义相机拉流图像设置
    config = rs.config()
    config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 15)
    config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 15)
    profile=pipeline.start(config)
    device = profile.get_device()
    # 深度图和RGB图对齐工具
    align_to = rs.stream.color
    align = rs.align(align_to)

    #初始化检测引擎
    # 注意：如果当前类别列表与模型不匹配，请更换合适的模型
    # 可选模型：game.engine (对应game_object.py的类别), fruits.engine (对应grasp_orange.py的类别)
    model_path = "./model/detect/pill.engine"
    detector = YoloV5(engine_path=model_path, conf=0.6, iou=0.8, shape=[640, 640])
    #打印计数器
    counter = 0

    while True:
        counter += 1
        frames = pipeline.wait_for_frames()
        aligned_frames = align.process(frames)  # bgr图像与depth对齐
        depth_frame = aligned_frames.get_depth_frame()
        if not depth_frame:
            print("NO RealSense Depth Frames")
            continue
        color_frame = frames.get_color_frame()
        if not color_frame:
            print("NO RealSense Color Frames")
            continue
        depth_image = np.asarray(depth_frame.get_data(), dtype=np.float32)
        color_image = np.asanyarray(color_frame.get_data()) # 返回的格式是bgr的与opencv的读取格式相同
        # 获得YOLOV5的检测输出
        results = detector.infer(color_image) #返回的框是和输入的图像对齐的

        if len(results) == 0:
            results = []
        else:
            results = filter_wrong_class(boxes=np.array(results), iou_threshold=0.9)

        centers = []
        # 开辟输出数据内存
        output = []
        for result in results:
            box = result[0:4].astype(np.int16)
            cls = result[5].astype(np.int8)
            threshold = result[4].astype(np.float16)

            # 检查类别索引是否在有效范围内
            if cls < 0 or cls >= len(CLASSES):
                print(f"检测到无效的类别索引: {cls}, 跳过该检测结果")
                continue

            box[0:4][box[0:4] < 0] = 0
            center = None
            image_rect = color_image[box[1]:box[3], box[0]:box[2]]

            # 根据类别确定中心点检测方法
            if CLASSES[cls] in ['box']:  # 使用几何中心的类别
                center = (box[0] + int(box[2] - box[0])//2,  box[1] + int(box[3] - box[1])//2)
            elif CLASSES[cls] in ['lltd', 'hxzq']:  # 使用红色标记的类别
                center_rect = find_red_pixel_center(image_rect, COLOR_THRESHOLD[CLASSES[cls]])
                if center_rect is None:
                    print(f"没有发现{CLASSES[cls]}的红色标记")
                    continue
                center = (box[0] + center_rect[1],  box[1] + center_rect[0])
            elif CLASSES[cls] in ['ysxtl', 'dyxa', 'akhm']:  # 使用蓝色标记的类别
                center_rect = find_blue_pixel_center(image_rect, COLOR_THRESHOLD[CLASSES[cls]])
                if center_rect is None:
                    print(f"没有发现{CLASSES[cls]}的蓝色标记")
                    continue
                center = (box[0] + center_rect[1],  box[1] + center_rect[0])
            else:
                # 对于其他类别，使用几何中心
                center = (box[0] + int(box[2] - box[0])//2,  box[1] + int(box[3] - box[1])//2)
                print(f"使用几何中心处理类别: {CLASSES[cls]}")

            if center is not None:
                # 确保中心点坐标在图像范围内
                if (0 <= center[0] < depth_image.shape[1] and
                    0 <= center[1] < depth_image.shape[0]):
                    depth = depth_image[center[1], center[0]]
                    if depth == 0.0:  # 修复：depth是numpy数组元素，不需要.data属性
                        print(f"深度数据异常，深度值为0")
                        continue
                    loc = pixel2real(K, KV, center, depth)
                    loc.append(cls)
                    output.append(loc)
                    centers.append(center)
                else:
                    print(f"中心点坐标超出图像范围: {center}")
                
        if VISUALIZE:
            visualize_yolo(color_image, results, centers)
            cv2.imshow("YOLO", color_image)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                cv2.destroyAllWindows()
                break
        
        output = sorted(output, key=lambda x: x[1], reverse=True)
        msg = Float64MultiArray()
        msg.data = np.array(output).flatten().tolist()
        if counter % 20 == 0:
            print(msg.data)
            print("*"*40)
        if msg.data:
            publish_detect(msg)
    pipeline.stop()
